import { MembershipRole } from '@packages/database/enums';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import type { DrizzleSupabaseClient } from '../index';
import type * as DTO from '../schema/types';

import {
  aliasedTable,
  and,
  asc,
  count,
  desc,
  eq,
  gt,
  gte,
  ilike,
  isNotNull,
  like,
  not,
  notExists,
  notInArray,
  or,
  sql,
} from 'drizzle-orm';

import { arrayAgg, coalesce } from '../orm';
import { authUsersTable } from '../schema/auth';
import {
  addressTable,
  agencyContractTable,
  agencyInsightTable,
  certificationTable,
  chatRoomMemberTable,
  chatRoomTable,
  connectionRequestTable,
  connectionTable,
  degreeTable,
  departmentContractTable,
  educationTable,
  experiencePositionTable,
  experienceSkillTable,
  experienceTable,
  feedbackTable,
  fullTimeAvailabilityTable,
  governmentAgencyTable,
  governmentContractTable,
  governmentDepartmentTable,
  governmentOfficeTable,
  governmentSubOfficeTable,
  invitationTable,
  invoiceTable,
  majorTable,
  membershipTable,
  messageTable,
  minorTable,
  officeContractTable,
  organizationAddressTable,
  organizationJoinRequestTable,
  organizationTable,
  pastPerformanceTable,
  payRateTable,
  positionTable,
  schoolTable,
  serviceTable,
  skillTable,
  subOfficeContractTable,
  subjectTable,
  userAddressTable,
  userCertificationTable,
  userDegreeTable,
  userPastPerformanceTable,
  userReviewRequestTable,
  userServiceTable,
  userSkillTable,
  usersTable,
} from '../schema/tables';

export async function getConsultantData(
  db: DrizzleSupabaseClient,
  params: { userId: string }
): Promise<DTO.ConsultantResponse> {
  const { userId } = params;

  return await db.transaction(async (tx) => {
    // User
    const u = aliasedTable(usersTable, 'u');
    const au = aliasedTable(authUsersTable, 'au');
    const pr = aliasedTable(payRateTable, 'pr');

    const [userData] = await tx
      .select({
        email: au.email,
        avatar: u.avatar,
        first_name: u.firstName,
        last_name: u.lastName,
        username: u.username,
        phone_number: u.phoneNumber,
        is_available: u.isAvailable,
        is_public: u.isPublic,
        unavailable_until: u.unavailableUntil,
        security_clearance: u.securityClearance,
        legal_status: u.legalStatus,
        pay_rate_label: pr.label,
        created_at: u.createdAt,
      })
      .from(u)
      .innerJoin(au, eq(u.id, au.id))
      .innerJoin(pr, eq(u.payRateId, pr.id))
      .where(eq(u.id, userId))
      .limit(1);

    if (!userData) {
      throw new Error('NO USER DATA FOUND');
    }

    const user = {
      ...userData,
      first_name: String(userData.first_name),
      email: String(userData.email),
      phone_number: String(userData.phone_number),
      unavailable_until: userData.unavailable_until?.toISOString(),
      created_at: userData.created_at?.toISOString(),
    };

    // User address
    const [user_address] = await tx
      .select({
        city: addressTable.city,
        state: addressTable.state,
      })
      .from(userAddressTable)
      .innerJoin(addressTable, eq(userAddressTable.addressId, addressTable.id))
      .where(eq(userAddressTable.userId, userId))
      .limit(1);

    if (!user_address) {
      throw new Error('NO USER ADDRESS DATA FOUND');
    }

    // Organization
    const [organization] = await tx
      .select({
        name: organizationTable.name,
        description: organizationTable.description,
        logo: organizationTable.logo,
        ein: organizationTable.ein,
        cage_code: organizationTable.cageCode,
        primary_naics_code: organizationTable.primaryNaicsCode,
      })
      .from(membershipTable)
      .innerJoin(
        organizationTable,
        eq(membershipTable.organizationId, organizationTable.id)
      )
      .where(eq(membershipTable.userId, userId))
      .limit(1);

    if (!organization) {
      throw new Error('NO ORGANIZATION DATA FOUND');
    }

    // Experience
    const experiences: DTO.ConsultantResponse['experiences'] = await tx
      .select({
        location_type: experienceTable.locationType,
        employment_type: experienceTable.employmentType,
        start_date: experienceTable.startDate,
        end_date: experienceTable.endDate,
        description: experienceTable.description,
        is_current_role: experienceTable.isCurrentRole,
        company_name: experienceTable.companyName,
        state: coalesce(experienceTable.state, '').mapWith(String),
        city: coalesce(experienceTable.city, '').mapWith(String),
        position_name: positionTable.name,
        skill_names: arrayAgg(skillTable.name),
      })
      .from(experienceTable)
      .innerJoin(
        experiencePositionTable,
        eq(experiencePositionTable.experienceId, experienceTable.id)
      )
      .innerJoin(
        positionTable,
        eq(experiencePositionTable.positionId, positionTable.id)
      )
      .leftJoin(
        experienceSkillTable,
        eq(experienceSkillTable.experienceId, experienceTable.id)
      )
      .leftJoin(skillTable, eq(experienceSkillTable.skillId, skillTable.id))
      .where(eq(experienceTable.userId, userId))
      .groupBy(experienceTable.id, positionTable.name);

    // Past performance
    const upp = aliasedTable(userPastPerformanceTable, 'upp');
    const pp = aliasedTable(pastPerformanceTable, 'pp');
    const pos = aliasedTable(positionTable, 'pos');
    const gc = aliasedTable(governmentContractTable, 'gc');
    const dc = aliasedTable(departmentContractTable, 'dc');
    const gd = aliasedTable(governmentDepartmentTable, 'gd');
    const ac = aliasedTable(agencyContractTable, 'ac');
    const ga = aliasedTable(governmentAgencyTable, 'ga');
    const oc = aliasedTable(officeContractTable, 'oc');
    const go = aliasedTable(governmentOfficeTable, 'go');
    const soc = aliasedTable(subOfficeContractTable, 'soc');
    const gso = aliasedTable(governmentSubOfficeTable, 'gso');

    const past_performances = await tx
      .select({
        award_date: pp.awardDate,
        description: pp.description,
        position_name: pos.name,
        contract_name: gc.name,
        contract_description: gc.description,
        contract_rfp_number: gc.rfpNumber,
        contract_value: gc.value,
        contract_department_name: gd.name,
        contract_agency_name: ga.name,
        contract_office_name: go.name,
        contract_sub_office_name: gso.name,
      })
      .from(upp)
      .innerJoin(pp, eq(upp.pastPerformanceId, pp.id))
      .innerJoin(pos, eq(pp.positionId, pos.id))
      .innerJoin(gc, eq(pp.governmentContractId, gc.id))
      .innerJoin(dc, eq(gc.id, dc.governmentContractId))
      .innerJoin(gd, eq(dc.departmentId, gd.id))
      .innerJoin(ac, eq(gc.id, ac.governmentContractId))
      .innerJoin(ga, eq(ac.agencyId, ga.id))
      .innerJoin(oc, eq(gc.id, oc.governmentContractId))
      .innerJoin(go, eq(oc.officeId, go.id))
      .innerJoin(soc, eq(gc.id, soc.governmentContractId))
      .innerJoin(gso, eq(soc.subOfficeId, gso.id))
      .where(eq(upp.userId, userId))
      .then((data) =>
        data.map((pp) => ({
          ...pp,
          description: String(pp.description || ''),
          contract_value: Number(pp.contract_value),
        }))
      );

    // Agency insights
    const ai = aliasedTable(agencyInsightTable, 'ai');
    const pos2 = aliasedTable(positionTable, 'pos2');
    const gd2 = aliasedTable(governmentDepartmentTable, 'gd2');
    const ga2 = aliasedTable(governmentAgencyTable, 'ga2');
    const go2 = aliasedTable(governmentOfficeTable, 'go2');
    const gso2 = aliasedTable(governmentSubOfficeTable, 'gso2');

    const agency_insights = await tx
      .select({
        description: ai.description,
        relationship_strength: ai.relationshipStrength,
        position_name: pos2.name,
        gov_department_name: gd2.name,
        gov_agency_name: ga2.name,
        gov_office_name: go2.name,
        gov_sub_office_name: gso2.name,
      })
      .from(ai)
      .innerJoin(pos2, eq(ai.positionId, pos2.id))
      .innerJoin(gd2, eq(ai.departmentId, gd2.id))
      .innerJoin(ga2, eq(ai.agencyId, ga2.id))
      .innerJoin(go2, eq(ai.officeId, go2.id))
      .innerJoin(gso2, eq(ai.subOfficeId, gso2.id))
      .where(eq(ai.userId, userId));

    // Expertise
    const us = aliasedTable(userSkillTable, 'us');
    const sk = aliasedTable(skillTable, 'sk');

    const expertise = await tx
      .select({
        skill_name: sk.name,
        expertise_level: us.expertiseLevel,
      })
      .from(us)
      .innerJoin(sk, eq(us.skillId, sk.id))
      .where(eq(us.userId, userId));

    // Certifications
    const uc = aliasedTable(userCertificationTable, 'uc');
    const ct = aliasedTable(certificationTable, 'ct');

    const certifications = await tx
      .select({
        name: ct.name,
        provider: ct.provider,
        credential_id: uc.credentialId,
        credential_url: uc.credentialUrl,
        issue_date: uc.issuedDate,
        expiration_date: uc.expirationDate,
      })
      .from(uc)
      .innerJoin(ct, eq(uc.certificationId, ct.id))
      .where(eq(uc.userId, userId));

    // Education
    const educationData = await tx
      .select({
        educationId: educationTable.id,
        schoolName: schoolTable.name,
        degreeName: degreeTable.name,
        userDegree: userDegreeTable,
      })
      .from(educationTable)
      .innerJoin(schoolTable, eq(educationTable.schoolId, schoolTable.id))
      .innerJoin(
        userDegreeTable,
        eq(educationTable.id, userDegreeTable.educationId)
      )
      .innerJoin(degreeTable, eq(userDegreeTable.degreeId, degreeTable.id))
      .where(eq(educationTable.userId, userId));

    type EducationType = DTO.ConsultantResponse['educations'][number];
    const educationsMap = new Map<number, EducationType>();

    for (const edu of educationData) {
      if (!educationsMap.has(edu.educationId)) {
        educationsMap.set(edu.educationId, {
          school_name: edu.schoolName,
          degrees: [],
        });
      }

      const [majors, minors] = await Promise.all([
        tx
          .select({ name: subjectTable.name })
          .from(majorTable)
          .innerJoin(subjectTable, eq(majorTable.subjectId, subjectTable.id))
          .where(eq(majorTable.userDegreeId, edu.userDegree.id)),
        tx
          .select({ name: subjectTable.name })
          .from(minorTable)
          .innerJoin(subjectTable, eq(minorTable.subjectId, subjectTable.id))
          .where(eq(minorTable.userDegreeId, edu.userDegree.id)),
      ]);

      educationsMap.get(edu.educationId)?.degrees.push({
        degree_name: edu.degreeName,
        start_date: edu.userDegree.startDate,
        end_date: edu.userDegree?.endDate ?? undefined,
        currently_studying: edu.userDegree?.currentlyStudying ?? false,
        major_names: majors.map((m) => m.name),
        minor_names: minors.map((m) => m.name),
      });
    }

    const educations = Array.from(educationsMap.values());

    // Services
    const usv = aliasedTable(userServiceTable, 'usv');
    const sv = aliasedTable(serviceTable, 'sv');
    const pr1 = aliasedTable(payRateTable, 'pr1');

    const services = await tx
      .select({
        service_name: sv.name,
        expertise_level: usv.expertiseLevel,
        fixed_price: usv.fixedPrice,
        pay_rate_label: pr1.label,
      })
      .from(usv)
      .innerJoin(sv, eq(usv.serviceId, sv.id))
      .innerJoin(pr1, eq(usv.payRateId, pr1.id))
      .where(eq(usv.userId, userId))
      .then((data) =>
        data.map((s) => ({
          ...s,
          expertise_level: String(s.expertise_level),
        }))
      );

    // Full time roles
    const fta = aliasedTable(fullTimeAvailabilityTable, 'fta');
    const pos3 = aliasedTable(positionTable, 'pos3');
    const pr2 = aliasedTable(payRateTable, 'pr2');

    const full_time_roles = await tx
      .select({
        position_name: pos3.name,
        pay_rate_label: pr2.label,
        location_type: fta.locationType,
        desired_start_date: fta.desiredStartDate,
      })
      .from(fta)
      .innerJoin(pos3, eq(fta.positionId, pos3.id))
      .innerJoin(pr2, eq(fta.payRateId, pr2.id))
      .where(eq(fta.userId, userId))
      .then((data) =>
        data.map((ft) => ({
          ...ft,
          location_type: String(ft.location_type),
        }))
      );

    return {
      id: userId,
      user,
      user_address,
      organization,
      experiences,
      past_performances,
      agency_insights,
      educations,
      expertise,
      certifications,
      services,
      full_time_roles,
    };
  });
}

export async function getConnectionStatus(
  db: DrizzleSupabaseClient,
  params: {
    targetUserId: string;
    currentUserId: string;
  }
): Promise<{
  connectionId?: number;
  connectionRequestId?: number;
}> {
  const { targetUserId, currentUserId } = params;

  return await db.transaction(async (tx) => {
    const user1Id = targetUserId < currentUserId ? targetUserId : currentUserId;
    const user2Id = targetUserId < currentUserId ? currentUserId : targetUserId;

    const [connection] = await tx
      .select({
        connectionId: connectionTable.id,
      })
      .from(connectionTable)
      .where(
        and(
          eq(connectionTable.user1Id, user1Id),
          eq(connectionTable.user2Id, user2Id)
        )
      )
      .limit(1);

    const { connectionId } = connection ?? {};

    if (connectionId) {
      return { connectionId };
    }

    const [connectionRequest] = await tx
      .select({
        connectionRequestId: connectionRequestTable.id,
      })
      .from(connectionRequestTable)
      .where(
        and(
          eq(connectionRequestTable.requesterId, currentUserId),
          eq(connectionRequestTable.receiverId, targetUserId)
        )
      )
      .limit(1);

    const { connectionRequestId } = connectionRequest ?? {};

    if (connectionRequestId) {
      return { connectionRequestId };
    }

    return {};
  });
}

export async function getChatRoomIdForUsers(
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  tx: PgTransaction<any, any, any>,
  params: { user1Id: string; user2Id: string }
) {
  const { user1Id, user2Id } = params;

  const m1 = aliasedTable(chatRoomMemberTable, 'm1');
  const m2 = aliasedTable(chatRoomMemberTable, 'm2');
  const m3 = aliasedTable(chatRoomMemberTable, 'm3');

  const [chatRoomMember] = await tx
    .select({ roomId: m1.roomId })
    .from(m1)
    .innerJoin(m2, eq(m1.roomId, m2.roomId))
    .where(
      and(
        eq(m1.userId, user1Id),
        eq(m2.userId, user2Id),
        notExists(
          tx
            .select({ one: sql<number>`1` })
            .from(m3)
            .where(
              and(
                eq(m3.roomId, m1.roomId),
                notInArray(m3.userId, [user1Id, user2Id])
              )
            )
        )
      )
    )
    .limit(1);

  const { roomId } = chatRoomMember ?? {};

  return roomId;
}

export type ConnectionSortField = 'connected_at' | 'first_name' | 'last_name';

export interface GetUserConnectionsParams {
  userId: string;
  sortBy?: ConnectionSortField;
  isAscending?: boolean;
  page?: number;
  perPage?: number;
}

export interface UserConnectionResponse {
  id: number;
  chatRoomId?: string;
  user1: Partial<DTO.UsersResponse>;
  user2: Partial<DTO.UsersResponse>;
  connectedAt: Date;
  initiatedByUser1: boolean;
}

export async function getUserConnections(
  db: DrizzleSupabaseClient,
  params: GetUserConnectionsParams
): Promise<UserConnectionResponse[]> {
  const {
    userId,
    page = 1,
    perPage = 10,
    sortBy = 'connected_at',
    isAscending = false,
  } = params;

  const c = aliasedTable(connectionTable, 'c');
  const u1 = aliasedTable(usersTable, 'u1');
  const u2 = aliasedTable(usersTable, 'u2');

  return await db.transaction(async (tx) => {
    const connectionsList = tx.$with('connections_list').as(
      tx
        .select({
          id: c.id,
          user1Id: c.user1Id,
          user2Id: c.user2Id,
          connectedAt: c.connectedAt,
          initiatedByUser1: c.initiatedByUser1,
          sortedFirstName:
            sql<string>`case when ${c.user1Id} = ${userId} then ${u2.firstName} else ${u1.firstName} end`.as(
              'sorted_first_name'
            ),
          sortedLastName:
            sql<string>`case when ${c.user1Id} = ${userId} then ${u2.lastName} else ${u1.lastName} end`.as(
              'sorted_last_name'
            ),
        })
        .from(c)
        .innerJoin(u1, eq(c.user1Id, u1.id))
        .innerJoin(u2, eq(c.user2Id, u2.id))
        .where(or(eq(c.user1Id, userId), eq(c.user2Id, userId)))
    );

    const result = await tx
      .with(connectionsList)
      .select({
        id: connectionsList.id,
        user1: {
          id: u1.id,
          avatar: u1.avatar,
          firstName: u1.firstName,
          lastName: u1.lastName,
          username: u1.username,
          headline: u1.headline,
        },
        user2: {
          id: u2.id,
          avatar: u2.avatar,
          firstName: u2.firstName,
          lastName: u2.lastName,
          username: u2.username,
          headline: u2.headline,
        },
        connectedAt: connectionsList.connectedAt,
        initiatedByUser1: connectionsList.initiatedByUser1,
      })
      .from(connectionsList)
      .innerJoin(u1, eq(connectionsList.user1Id, u1.id))
      .innerJoin(u2, eq(connectionsList.user2Id, u2.id))
      .orderBy(
        sortBy === 'first_name' && isAscending
          ? asc(connectionsList.sortedFirstName)
          : sortBy === 'first_name' && !isAscending
            ? desc(connectionsList.sortedFirstName)
            : sortBy === 'last_name' && isAscending
              ? asc(connectionsList.sortedLastName)
              : sortBy === 'last_name' && !isAscending
                ? desc(connectionsList.sortedLastName)
                : sortBy === 'connected_at' && isAscending
                  ? asc(connectionsList.connectedAt)
                  : desc(connectionsList.connectedAt)
      )
      .limit(perPage)
      .offset((page - 1) * perPage);

    const connectionsWithChatRooms = await Promise.all(
      result.map(async (connection) => {
        const chatRoomId = await getChatRoomIdForUsers(tx, {
          user1Id: connection.user1.id,
          user2Id: connection.user2.id,
        });

        return {
          ...connection,
          chatRoomId,
        };
      })
    );

    return connectionsWithChatRooms;
  });
}

export interface GetUserChatRoomsParams {
  userId: string;
  withMessagesOnly?: boolean;
  isArchived?: boolean;
  page?: number;
  perPage?: number;
  searchName?: string;
  sortByName?: boolean;
}

export interface UserChatRoomsResponse {
  roomId: string;
  creatorId: string;
  lastMessageAt: Date;
  isDisabled: boolean | null;
  isUnread: boolean;
  otherUsers: Array<{
    id: string;
    avatar: string | null;
    firstName: string | null;
    lastName: string | null;
    username: string | null;
    joinedAt: Date;
    isArchived: boolean | null;
  }>;
  lastMessage?: DTO.MessageResponse | null;
}

export async function getUserChatRooms(
  db: DrizzleSupabaseClient,
  params: GetUserChatRoomsParams
): Promise<UserChatRoomsResponse[]> {
  const {
    userId,
    withMessagesOnly = false,
    isArchived,
    page = 1,
    perPage = 10,
    searchName = null,
    sortByName = false,
  } = params;

  const crm = aliasedTable(chatRoomMemberTable, 'crm');
  const otherCrm = aliasedTable(chatRoomMemberTable, 'other_crm');
  const msg = aliasedTable(messageTable, 'msg');
  const usr = aliasedTable(usersTable, 'usr');

  return await db.transaction(async (tx) => {
    const baseRooms = await tx
      .selectDistinct({
        roomId: chatRoomTable.id,
        creatorId: chatRoomTable.creatorId,
        lastMessageAt: chatRoomTable.lastMessageAt,
        isDisabled: chatRoomTable.isDisabled,
        firstName: usr.firstName,
        isUnread: not(crm.hasRead).as<boolean>('isUnread'),
      })
      .from(chatRoomTable)
      .innerJoin(crm, eq(crm.roomId, chatRoomTable.id))
      .innerJoin(otherCrm, eq(otherCrm.roomId, chatRoomTable.id))
      .innerJoin(usr, eq(usr.id, otherCrm.userId))
      .leftJoin(msg, eq(msg.roomId, chatRoomTable.id))
      .where(
        and(
          eq(crm.userId, userId),
          not(eq(otherCrm.userId, userId)),
          eq(chatRoomTable.isDisabled, false),
          withMessagesOnly ? isNotNull(msg.id) : undefined,
          isArchived !== undefined ? eq(crm.isArchived, isArchived) : undefined,
          searchName
            ? or(
                like(
                  sql`lower(concat(${usr.firstName}, ' ', ${usr.lastName}))`,
                  `%${searchName.toLowerCase()}%`
                ),
                like(
                  sql`lower(${usr.username})`,
                  `%${searchName.toLowerCase()}%`
                )
              )
            : undefined
        )
      )
      .orderBy(
        sortByName
          ? sql`${usr.firstName} nulls last`
          : desc(chatRoomTable.lastMessageAt)
      )
      .limit(perPage)
      .offset((page - 1) * perPage);

    const result: UserChatRoomsResponse[] = [];

    for (const room of baseRooms) {
      let lastMessage: DTO.MessageResponse | null = null;

      if (withMessagesOnly) {
        const [message] = await tx
          .select()
          .from(msg)
          .where(eq(msg.roomId, room.roomId))
          .orderBy(desc(msg.createdAt))
          .limit(1);

        lastMessage = message ?? null;
      }

      const otherUsers = await tx
        .select({
          id: otherCrm.userId,
          avatar: usr.avatar,
          firstName: usr.firstName,
          lastName: usr.lastName,
          username: usr.username,
          joinedAt: otherCrm.joinedAt,
          isArchived: otherCrm.isArchived,
        })
        .from(otherCrm)
        .innerJoin(usr, eq(usr.id, otherCrm.userId))
        .where(
          and(
            eq(otherCrm.roomId, room.roomId),
            not(eq(otherCrm.userId, userId))
          )
        );

      const { firstName, ...chatRoom } = room;

      result.push({
        ...chatRoom,
        lastMessage,
        otherUsers,
      });
    }

    return result;
  });
}

export interface GetInvoiceByUserIdResponse extends DTO.InvoiceResponse {
  total: number;
}

export async function getInvoicesByUserId(
  db: DrizzleSupabaseClient,
  params: {
    userId?: string;
    page?: number;
    perPage?: number;
    sortBy?: 'date' | 'amount';
    isAscending?: boolean;
  }
): Promise<GetInvoiceByUserIdResponse[]> {
  const {
    page = 1,
    perPage = 10,
    sortBy = 'date',
    isAscending = false,
    userId = sql<string>`auth.uid()`,
  } = params;

  return await db.transaction(async (tx) => {
    const [membershipResult] = await tx
      .select({ organizationId: membershipTable.organizationId })
      .from(membershipTable)
      .where(
        and(
          eq(membershipTable.userId, userId),
          eq(membershipTable.roleId, MembershipRole.Owner)
        )
      )
      .limit(1);

    const { organizationId } = membershipResult!;

    if (!organizationId) {
      return [];
    }

    const [invoiceResult] = await tx
      .select({ total: count() })
      .from(invoiceTable)
      .where(eq(invoiceTable.organizationId, organizationId));

    const { total } = invoiceResult ?? {};

    const result = await tx
      .select({
        id: invoiceTable.id,
        organizationId: invoiceTable.organizationId,
        stripeInvoiceId: invoiceTable.stripeInvoiceId,
        amount: invoiceTable.amount,
        status: invoiceTable.status,
        invoicePdf: invoiceTable.invoicePdf,
        createdAt: invoiceTable.createdAt,
        total: sql<number>`${total}`,
      })
      .from(invoiceTable)
      .where(eq(invoiceTable.organizationId, organizationId))
      .orderBy(
        sortBy === 'date'
          ? isAscending
            ? asc(invoiceTable.createdAt)
            : desc(invoiceTable.createdAt)
          : sortBy === 'amount'
            ? isAscending
              ? asc(invoiceTable.amount)
              : desc(invoiceTable.amount)
            : desc(invoiceTable.createdAt)
      )
      .limit(perPage)
      .offset((page - 1) * perPage);

    return result;
  });
}

export async function getAssociatedOrganizationByEmail(
  db: DrizzleSupabaseClient,
  params: { email: string }
): Promise<DTO.OrganizationResponse | undefined> {
  const userDomain = params.email.split('@')[1];

  return await db.transaction(async (tx) => {
    const [organization] = await tx
      .selectDistinct({
        id: organizationTable.id,
        name: organizationTable.name,
        description: organizationTable.description,
        logo: organizationTable.logo,
        ein: organizationTable.ein,
        primaryNaicsCode: organizationTable.primaryNaicsCode,
        cageCode: organizationTable.cageCode,
        raw: authUsersTable.rawAppMetaData,
      })
      .from(membershipTable)
      .innerJoin(
        organizationTable,
        eq(membershipTable.organizationId, organizationTable.id)
      )
      .innerJoin(authUsersTable, eq(membershipTable.userId, authUsersTable.id))
      .where(
        and(
          eq(membershipTable.roleId, MembershipRole.Owner),
          eq(sql`split_part(${authUsersTable.email}, '@', 2)`, userDomain),
          eq(
            sql<boolean>`coalesce((${authUsersTable.rawAppMetaData}->>'isClient')::boolean, false)`,
            true
          )
        )
      )
      .limit(1);
    return organization ? { ...organization, raw: undefined } : undefined;
  });
}

interface OrganizationAdminMembershipBasicResponse {
  role: number;
  organization: Pick<DTO.OrganizationResponse, 'id' | 'name'>;
  user: Partial<DTO.UsersResponse> & { email: string | null };
}

export async function getOrganizationAdminMembershipsBasic(
  db: DrizzleSupabaseClient,
  params: { organizationId: string }
): Promise<OrganizationAdminMembershipBasicResponse[]> {
  const { organizationId } = params;
  const au = aliasedTable(authUsersTable, 'au');

  return await db.transaction(async (tx) => {
    const memberData = tx.$with('member_data').as(
      tx
        .select({
          userId: membershipTable.userId,
          roleId: membershipTable.roleId,
          orgId: organizationTable.id,
          orgName: organizationTable.name,
        })
        .from(membershipTable)
        .innerJoin(
          organizationTable,
          eq(membershipTable.organizationId, organizationTable.id)
        )
        .where(
          and(
            eq(membershipTable.organizationId, organizationId),
            gte(membershipTable.roleId, MembershipRole.Admin)
          )
        )
    );

    return await tx
      .with(memberData)
      .select({
        role: memberData.roleId,
        user: {
          id: usersTable.id,
          avatar: usersTable.avatar,
          email: au.email,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
          username: usersTable.username,
        },
        organization: {
          id: memberData.orgId,
          name: memberData.orgName,
        },
      })
      .from(memberData)
      .innerJoin(usersTable, eq(memberData.userId, usersTable.id))
      .innerJoin(au, eq(usersTable.id, au.id))
      .where(
        and(
          eq(
            sql<boolean>`coalesce((${au.rawAppMetaData}->>'isClient')::boolean, false)`,
            true
          ),
          eq(
            sql<boolean>`coalesce((${au.rawAppMetaData}->>'isOnboarded')::boolean, false)`,
            true
          )
        )
      );
  });
}

export interface GetOrganizationJoinRequestsResponse
  extends DTO.OrganizationJoinRequestResponse {
  total: number;
}

export async function getOrganizationJoinRequests(
  db: DrizzleSupabaseClient,
  params: {
    organizationId: string;
    statusFilter?: DTO.OrganizationJoinRequestStatusEnumType;
    emailSearch?: string;
    page?: number;
    perPage?: number;
    sortBy?: string;
    isAscending?: boolean;
    operator?: 'and' | 'or';
  }
): Promise<GetOrganizationJoinRequestsResponse[]> {
  const {
    organizationId,
    statusFilter,
    emailSearch,
    page = 1,
    perPage = 10,
    sortBy = 'createdAt',
    isAscending = false,
    operator = 'and',
  } = params;

  const countFilter =
    operator === 'and'
      ? and(
          eq(organizationJoinRequestTable.organizationId, organizationId),
          statusFilter
            ? eq(organizationJoinRequestTable.status, statusFilter)
            : undefined
        )
      : or(
          eq(organizationJoinRequestTable.organizationId, organizationId),
          statusFilter
            ? eq(organizationJoinRequestTable.status, statusFilter)
            : undefined
        );

  const emailFilter = emailSearch
    ? ilike(organizationJoinRequestTable.email, `%${emailSearch}%`)
    : undefined;

  return await db.transaction(async (tx) => {
    const [orgJoinRequestsResults] = await tx
      .select({ total: count() })
      .from(organizationJoinRequestTable)
      .where(and(countFilter, emailFilter));

    const { total } = orgJoinRequestsResults!;

    const result = await tx
      .select({
        id: organizationJoinRequestTable.id,
        organizationId: organizationJoinRequestTable.organizationId,
        userId: organizationJoinRequestTable.userId,
        email: organizationJoinRequestTable.email,
        status: organizationJoinRequestTable.status,
        createdAt: organizationJoinRequestTable.createdAt,
        processedBy: {
          id: usersTable.id,
          avatar: usersTable.avatar,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
        },
        total: sql<number>`${total}`,
      })
      .from(organizationJoinRequestTable)
      .leftJoin(
        usersTable,
        eq(organizationJoinRequestTable.processedBy, usersTable.id)
      )
      .where(and(countFilter, emailFilter))
      .orderBy(
        sql`case when ${organizationJoinRequestTable.status} = 'PENDING' then 0 else 1 end`,
        sortBy === 'createdAt'
          ? isAscending
            ? asc(organizationJoinRequestTable.createdAt)
            : desc(organizationJoinRequestTable.createdAt)
          : sortBy === 'email'
            ? isAscending
              ? asc(organizationJoinRequestTable.email)
              : desc(organizationJoinRequestTable.email)
            : desc(organizationJoinRequestTable.createdAt)
      )
      .limit(perPage)
      .offset((page - 1) * perPage);

    return result;
  });
}

export async function calculateProfileCompletion(
  db: DrizzleSupabaseClient,
  params: { userId: string }
): Promise<DTO.ConsultantProfileCompletion> {
  const { userId } = params;

  return await db.transaction(async (tx) => {
    const [user] = await tx
      .select({
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        avatar: usersTable.avatar,
        username: usersTable.username,
        phoneNumber: usersTable.phoneNumber,
        payRateId: usersTable.payRateId,
      })
      .from(usersTable)
      .where(eq(usersTable.id, userId))
      .limit(1);

    if (!user) {
      throw new Error('User not found');
    }

    const basicInfo = Boolean(
      user.firstName &&
        user.lastName &&
        user.phoneNumber &&
        user.username &&
        user.payRateId
    );

    const profilePicture = Boolean(user.avatar);

    const [
      hasWorkExperience,
      hasPastPerformance,
      hasEducation,
      hasExpertise,
      hasServices,
    ] = await Promise.all([
      tx
        .select({ exists: sql<number>`1` })
        .from(experienceTable)
        .where(eq(experienceTable.userId, userId))
        .then((res) => res.length > 0),

      tx
        .select({ exists: sql<number>`1` })
        .from(userPastPerformanceTable)
        .where(eq(userPastPerformanceTable.userId, userId))
        .then((res) => res.length > 0),

      tx
        .select({ exists: sql<number>`1` })
        .from(educationTable)
        .where(eq(educationTable.userId, userId))
        .then((res) => res.length > 0),

      tx
        .select({ count: sql<number>`count(*)`.mapWith(Number) })
        .from(userSkillTable)
        .where(eq(userSkillTable.userId, userId))
        .then((res) => (res[0]?.count ?? 0) >= 3),

      tx
        .select({ exists: sql<number>`1` })
        .from(userServiceTable)
        .where(eq(userServiceTable.userId, userId))
        .then((res) => res.length > 0),
    ]);

    const isComplete =
      basicInfo &&
      profilePicture &&
      hasWorkExperience &&
      hasPastPerformance &&
      hasEducation &&
      hasExpertise &&
      hasServices;

    return {
      userId,
      basicInfo,
      profilePicture,
      workExperience: hasWorkExperience,
      pastPerformance: hasPastPerformance,
      education: hasEducation,
      expertise: hasExpertise,
      services: hasServices,
      isComplete,
    };
  });
}

export async function getFeedbacksByType(
  db: DrizzleSupabaseClient,
  params: { type: DTO.FeedbackTypeEnumType }
): Promise<DTO.FeedbackResponse[]> {
  const { type } = params;

  return await db.transaction(async (tx) => {
    return await tx
      .select({
        id: feedbackTable.id,
        user: {
          id: usersTable.id,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
          avatar: usersTable.avatar,
          username: usersTable.username,
          headline: usersTable.headline,
        },
        rating: feedbackTable.rating,
        type: feedbackTable.type,
        title: feedbackTable.title,
        description: feedbackTable.description,
        issueType: feedbackTable.issueType,
        createdAt: feedbackTable.createdAt,
        updatedAt: feedbackTable.updatedAt,
      })
      .from(feedbackTable)
      .innerJoin(usersTable, eq(feedbackTable.userId, usersTable.id))
      .where(eq(feedbackTable.type, type))
      .orderBy(desc(feedbackTable.createdAt));
  });
}

export async function getUserReviewRequestByToken(
  db: DrizzleSupabaseClient,
  params: { token: string }
): Promise<DTO.UserReviewRequestResponse | undefined> {
  const { token } = params;

  return await db.transaction(async (tx) => {
    const [userReviewRequest] = await tx
      .select({
        id: userReviewRequestTable.id,
        requester: {
          id: usersTable.id,
          avatar: usersTable.avatar,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
          username: usersTable.username,
        },
        reviewerEmail: userReviewRequestTable.reviewerEmail,
        relationship: userReviewRequestTable.relationship,
        content: userReviewRequestTable.content,
        token: userReviewRequestTable.token,
        expirationDate: userReviewRequestTable.expirationDate,
        createdAt: userReviewRequestTable.createdAt,
      })
      .from(userReviewRequestTable)
      .innerJoin(
        usersTable,
        eq(userReviewRequestTable.requesterId, usersTable.id)
      )
      .where(
        and(
          eq(userReviewRequestTable.token, token),
          gt(userReviewRequestTable.expirationDate, new Date())
        )
      )
      .limit(1);

    return userReviewRequest;
  });
}

export const invitationSelect = {
  id: invitationTable.id,
  email: invitationTable.email,
  firstName: invitationTable.firstName,
  lastName: invitationTable.lastName,
  roleId: invitationTable.roleId,
  code: invitationTable.code,
  status: invitationTable.status,
  inviter: {
    id: usersTable.id,
    firstName: usersTable.firstName,
    lastName: usersTable.lastName,
  },
  organization: {
    id: organizationTable.id,
    name: organizationTable.name,
  },
};

export async function getInvitationByInviteCode(
  db: DrizzleSupabaseClient,
  params: { code: string; status?: DTO.InvitationStatusTypeEnumType }
): Promise<DTO.InvitationResponse | undefined> {
  const { code, status } = params;

  return await db.transaction(async (tx) => {
    const [invitation] = await tx
      .select(invitationSelect)
      .from(invitationTable)
      .innerJoin(usersTable, eq(usersTable.id, invitationTable.inviterId))
      .innerJoin(
        organizationTable,
        eq(organizationTable.id, invitationTable.organizationId)
      )
      .where(
        and(
          eq(invitationTable.code, code),
          !!status ? eq(invitationTable.status, status) : undefined
        )
      )
      .limit(1);
    return invitation;
  });
}

export async function getInvitationsByOrganizationId(
  db: DrizzleSupabaseClient,
  params: { organizationId: string }
): Promise<DTO.InvitationResponse[]> {
  const { organizationId } = params;

  return await db.transaction(async (tx) => {
    return await tx
      .select(invitationSelect)
      .from(invitationTable)
      .innerJoin(usersTable, eq(usersTable.id, invitationTable.inviterId))
      .innerJoin(
        organizationTable,
        eq(organizationTable.id, invitationTable.organizationId)
      )
      .where(eq(invitationTable.organizationId, organizationId));
  });
}

export type MembersAndInvitationsResponse = {
  members: DTO.OrganizationMemberResponse[];
  invitations: DTO.InvitationResponse[];
};

export async function getMembersAndInvitationByOrgId(
  db: DrizzleSupabaseClient,
  params: { organizationId: string }
): Promise<MembersAndInvitationsResponse> {
  const { organizationId } = params;

  const u = aliasedTable(usersTable, 'u');
  const au = aliasedTable(authUsersTable, 'au');

  return await db.transaction(async (tx) => {
    const members = await tx
      .select({
        membershipId: membershipTable.id,
        role: membershipTable.roleId,
        data: {
          id: u.id,
          firstName: u.firstName,
          lastName: u.lastName,
          phoneNumber: u.phoneNumber,
        },
        email: au.email,
        status: sql<string>`case when coalesce((${au.rawAppMetaData}->>'isOnboarded')::boolean, false) then 'active' else 'inactive' end`,
      })
      .from(membershipTable)
      .innerJoin(u, eq(u.id, membershipTable.userId))
      .innerJoin(au, eq(au.id, membershipTable.userId))
      .where(eq(membershipTable.organizationId, organizationId));

    const invitations = await tx
      .select(invitationSelect)
      .from(invitationTable)
      .innerJoin(u, eq(u.id, invitationTable.inviterId))
      .innerJoin(
        organizationTable,
        eq(organizationTable.id, invitationTable.organizationId)
      )
      .where(eq(invitationTable.organizationId, organizationId));

    return { members, invitations };
  });
}

export async function getBasicMembershipByUserId(
  db: DrizzleSupabaseClient,
  params: { userId: string }
): Promise<Omit<DTO.MembershipResponse, 'user'>> {
  const { userId } = params;

  return db.transaction(async (tx) => {
    const [membership] = await tx
      .select({
        roleId: membershipTable.roleId,
        organization: {
          id: organizationTable.id,
          ein: organizationTable.ein,
          name: organizationTable.name,
          logo: organizationTable.logo,
          description: organizationTable.description,
          cageCode: organizationTable.cageCode,
          primaryNaicsCode: organizationTable.primaryNaicsCode,
        },
        address: {
          id: addressTable.id,
          address1: addressTable.address1,
          address2: addressTable.address2,
          city: addressTable.city,
          state: addressTable.state,
          zipCode: addressTable.zipCode,
        },
      })
      .from(membershipTable)
      .innerJoin(
        organizationTable,
        eq(membershipTable.organizationId, organizationTable.id)
      )
      .leftJoin(
        organizationAddressTable,
        eq(organizationTable.id, organizationAddressTable.organizationId)
      )
      .leftJoin(
        addressTable,
        eq(organizationAddressTable.addressId, addressTable.id)
      )
      .where(eq(membershipTable.userId, userId))
      .limit(1);

    const { roleId, organization, address } = membership ?? {};

    if (roleId === undefined || !organization) {
      throw new Error('❌ FAILED TO RETRIEVE MEMBERSHIP');
    }

    return {
      roleId,
      organization: {
        ...organization,
        address,
      },
    };
  });
}

export async function getUserWithAddressById(
  db: DrizzleSupabaseClient,
  params: { userId: string }
): Promise<DTO.UserWithAddressResponse> {
  const { userId } = params;

  return await db.transaction(async (tx) => {
    const [user] = await tx
      .select({
        id: usersTable.id,
        avatar: usersTable.avatar,
        username: usersTable.username,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        phoneNumber: usersTable.phoneNumber,
        headline: usersTable.headline,
        biography: usersTable.biography,
        payRateId: usersTable.payRateId,
        securityClearance: usersTable.securityClearance,
        legalStatus: usersTable.legalStatus,
        address: {
          id: addressTable.id,
          address1: addressTable.address1,
          address2: addressTable.address2,
          city: addressTable.city,
          state: addressTable.state,
          zipCode: addressTable.zipCode,
        },
      })
      .from(usersTable)
      .leftJoin(userAddressTable, eq(usersTable.id, userAddressTable.userId))
      .leftJoin(addressTable, eq(userAddressTable.addressId, addressTable.id))
      .where(eq(usersTable.id, userId))
      .limit(1);

    if (!user) {
      throw new Error('❌ FAILED TO RETRIEVE USER');
    }

    return user;
  });
}

export async function getUserById(
  db: DrizzleSupabaseClient,
  params: { userId: string }
): Promise<DTO.UsersResponse> {
  const { userId } = params;

  return await db.transaction(async (tx) => {
    const [user] = await tx
      .select({
        id: usersTable.id,
        avatar: usersTable.avatar,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        username: usersTable.username,
        headline: usersTable.headline,
        biography: usersTable.biography,
        fteAvailable: usersTable.fteAvailable,
        obEmailSent: usersTable.obEmailSent,
        hasCompletedTour: usersTable.hasCompletedTour,
        payRateId: usersTable.payRateId,
        securityClearance: usersTable.securityClearance,
        legalStatus: usersTable.legalStatus,
        isPublic: usersTable.isPublic,
        isAvailable: usersTable.isAvailable,
        unavailableUntil: usersTable.unavailableUntil,
        createdAt: usersTable.createdAt,
      })
      .from(usersTable)
      .where(eq(usersTable.id, userId))
      .limit(1);

    if (!user) {
      throw new Error('❌ FAILED TO RETRIEVE USER');
    }

    return user;
  });
}

export async function getUserByUsername(
  db: DrizzleSupabaseClient,
  params: { username: string; authenticated?: boolean }
) {
  const { username, authenticated = true } = params;

  return await db.transaction(async (tx) => {
    const [user] = await tx
      .select({
        id: usersTable.id,
        avatar: usersTable.avatar,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        username: usersTable.username,
        isPublic: usersTable.isPublic,
      })
      .from(usersTable)
      .where(eq(usersTable.username, username))
      .limit(1);

    if (!user) {
      throw new Error('❌ FAILED TO RETRIEVE USER');
    }

    if (!authenticated) {
      return { ...user, lastName: null };
    }

    return user;
  });
}
