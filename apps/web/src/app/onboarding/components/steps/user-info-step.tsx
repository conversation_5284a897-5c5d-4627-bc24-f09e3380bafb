'use client';

import { OnboardingStep } from '@/types/onboarding';

import { Fragment } from 'react';

import { OnboardingSectionHeader } from '@/app/onboarding/components/onboarding-section-header';
import { useOnboardingStep } from '@/app/onboarding/hooks/use-onboarding-step';
import { UserInfoForm } from '@/components/domain/user/user-info-form';
import { UserInfoFormSkeleton } from '@/components/domain/user/user-info-form-skeleton';
import { If } from '@packages/ui/if';

import { useUserSession, useUserWithAddressQuery } from '@/core/hooks/user';
import { canChangeBilling, canInviteUsers } from '@/core/permissions';

const TITLE = 'Account Information';
const DESCRIPTION = 'Add your user account details.';

export const UserInfoStep = ({ userId }: { userId: string }) => {
  const { setCurrentStep } = useOnboardingStep();
  const {
    data: user,
    refetch,
    isPending: isUserLoading,
  } = useUserWithAddressQuery(userId);

  const currentUser = useUserSession();
  const isClient = currentUser?.user?.isClient;
  const currentUserRole = currentUser?.user?.role!;

  const canManageBilling = canChangeBilling(currentUserRole);

  console.log({ canManageBilling, currentUserRole });

  const onPreviousStep = () => {
    setCurrentStep(OnboardingStep.OrgInfoStep);
  };

  const onSuccess = () => {
    refetch();
    if (isClient) {
      if (canInviteUsers(currentUserRole)) {
        setCurrentStep(OnboardingStep.InviteEmployeesStep);
      } else {
        setCurrentStep(OnboardingStep.SummaryStep);
      }
    } else {
      setCurrentStep(OnboardingStep.WorkExperienceStep);
    }
  };

  return (
    <Fragment>
      <OnboardingSectionHeader
        title={TITLE}
        description={DESCRIPTION}
        onPreviousStep={canManageBilling ? onPreviousStep : undefined}
      />
      <If
        condition={!isUserLoading}
        fallback={<UserInfoFormSkeleton isClient={isClient} />}
      >
        <UserInfoForm user={user} onSuccess={onSuccess} onBoardingMode />
      </If>
    </Fragment>
  );
};
