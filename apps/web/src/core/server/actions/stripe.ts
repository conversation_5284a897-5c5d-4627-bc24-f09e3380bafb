'use server';

import type { DrizzleSupabaseClient } from '@packages/database';
import type { MembershipRole } from '@packages/database/enums';
import type { SubscriptionPlanType } from '@packages/database/types';
import type { User as AuthUser } from '@packages/supabase/types';
import type { Stripe } from 'stripe';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { membershipTable, organizationTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';
import { getURLFromRedirectError } from 'next/dist/client/components/redirect';
import { isRedirectError } from 'next/dist/client/components/redirect-error';
import { permanentRedirect, redirect } from 'next/navigation';

import { SiteConfig, StripeConfig } from '@/configuration';
import { canChangeBilling } from '@/core/permissions';
import {
  cancelSubscriptionSchema,
  subscriptionFormSchema,
  updateSubscriptionSchema,
} from '@/core/schemas/subscription';
import { authActionClient } from '@/core/server/actions/safe-action';
import { stripe } from '@/lib/stripe';

export const createSubscriptionAction = authActionClient
  .schema(subscriptionFormSchema)
  .metadata({ name: 'create-subscription' })
  .action(
    async ({ ctx: { authUser }, parsedInput: { plan, additionalSeats } }) => {
      const userId = authUser.id;
      const email = authUser.email as string;
      const priceId = getPriceIdByPlan(plan);
      const seats = getSeatsByPlan(plan);
      const quantity = seats + (additionalSeats ?? 0);

      try {
        const db = await createDrizzleSupabaseClient();
        const organizationId = await getOrganizationIdAndValidatePermissions(
          db,
          authUser
        );

        let customerId: string;
        const { data: customer } = await stripe.customers.list({ email });

        if (!customer.length) {
          const newCustomer = await stripe.customers.create({ email });
          customerId = newCustomer.id;
        } else {
          customerId = customer[0]!.id;
        }

        const subscription = await stripe.subscriptions.create({
          customer: customerId,
          items: [{ price: priceId, quantity }],
          payment_behavior: 'default_incomplete',
          payment_settings: { save_default_payment_method: 'on_subscription' },
          expand: ['latest_invoice.payment_intent'],
          metadata: { plan, organizationId },
          cancel_at_period_end: false,
        });

        const invoice = subscription.latest_invoice as Stripe.Invoice;
        const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;

        if (!paymentIntent.client_secret) {
          logger.error(
            { paymentIntent },
            '❌ FAILED TO GET CLIENT SECRET FROM PAYMENT INTENT'
          );
          throw new Error('❌ FAILED TO GET CLIENT SECRET FROM PAYMENT INTENT');
        }

        logger.info(
          { userId, organizationId, subscriptionId: subscription.id },
          '✅ SUCCESSFULLY CREATED SUBSCRIPTION'
        );

        redirect(
          `/checkout?clientSecret=${paymentIntent.client_secret}&subscriptionId=${subscription.id}`
        );
        //
      } catch (error) {
        if (isRedirectError(error)) {
          redirect(getURLFromRedirectError(error));
        }
        logger.error({ error }, '❌ ERROR CREATING SUBSCRIPTION');
        throw new Error('❌ ERROR CREATING SUBSCRIPTION');
      }
    }
  );

export const updateSubscriptionAction = authActionClient
  .schema(updateSubscriptionSchema)
  .metadata({ name: 'update-subscription' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { plan: newPlan, additionalSeats, subscriptionId },
    }) => {
      const userId = authUser.id;

      try {
        const db = await createDrizzleSupabaseClient();
        const organizationId = await getOrganizationIdAndValidatePermissions(
          db,
          authUser
        );

        const currentSubscription =
          await stripe.subscriptions.retrieve(subscriptionId);

        const priceId = getPriceIdByPlan(newPlan);
        const seats = getSeatsByPlan(newPlan);
        const newQuantity = seats + (additionalSeats ?? 0);

        logger.info({ newPlan, seats, newQuantity });

        await stripe.subscriptions.update(subscriptionId, {
          items: [
            {
              id: currentSubscription.items.data[0]?.id,
              price: priceId,
              quantity: newQuantity,
            },
          ],
          proration_behavior: 'create_prorations',
          metadata: { plan: newPlan, organizationId },
          cancel_at_period_end: false,
        });

        logger.info(
          { userId, organizationId },
          '✅ SUCCESSFULLY UPDATED SUBSCRIPTION'
        );
      } catch (error) {
        logger.error({ error }, '❌ ERROR UPDATING SUBSCRIPTION');
        throw new Error('❌ ERROR UPDATING SUBSCRIPTION');
      }
    }
  );

export const cancelSubscriptionAction = authActionClient
  .schema(cancelSubscriptionSchema)
  .metadata({ name: 'cancel-subscription' })
  .action(async ({ ctx: { authUser }, parsedInput: { subscriptionId } }) => {
    const userId = authUser.id;

    try {
      const db = await createDrizzleSupabaseClient();
      await getOrganizationIdAndValidatePermissions(db, authUser);

      const subscription = await stripe.subscriptions.retrieve(subscriptionId);

      const cancel_at = getCancelAtDate(subscription.created);
      const currentDate = Date.now() / 1000;

      if (cancel_at <= currentDate) {
        const canceledSubscription =
          await stripe.subscriptions.cancel(subscriptionId);

        if (canceledSubscription.status !== 'canceled') {
          logger.error(
            { userId, subscriptionId },
            '❌ FAILED TO CANCEL SUBSCRIPTION'
          );
          throw new Error('❌ FAILED TO CANCEL SUBSCRIPTION');
        }

        logger.info(
          { userId, subscriptionId },
          '✅ SUCCESSFULLY CANCELLED SUBSCRIPTION'
        );

        return {
          canceled: true,
          message: 'Subscription cancelled successfully',
        };
      }

      await stripe.subscriptions.update(subscriptionId, {
        cancel_at,
      });

      logger.info(
        { userId, subscriptionId, cancel_at },
        '✅ SUCCESSFULLY UPDATED SUBSCRIPTION TO CANCEL AT DATE'
      );

      return {
        canceled: false,
        message: 'Subscription scheduled to cancel automatically',
      };
    } catch (error) {
      logger.error({ error }, '❌ ERROR CANCELING SUBSCRIPTION');
      throw new Error('❌ ERROR CANCELING SUBSCRIPTION');
    }
  });

// -------------------------------------------------------------------------------
//                              UTILITY FUNCTIONS
// -------------------------------------------------------------------------------

type QueryResponse = {
  role: MembershipRole;
  organizationId: string;
};

export async function getOrganizationIdAndValidatePermissions(
  db: DrizzleSupabaseClient,
  authUser: AuthUser
) {
  const userId = authUser.id;
  const userIsClient = authUser.app_metadata.isClient === true;
  const userIsOnboarded = authUser.app_metadata.isOnboarded === true;

  if (!userIsClient) {
    logger.error({ userId }, '❌ USER IS NOT A CLIENT');
    throw new Error('❌ USER IS NOT A CLIENT');
  }

  if (!userIsOnboarded) {
    logger.error({ userId }, '❌ USER IS NOT ONBOARDED');
    throw new Error('❌ USER IS NOT ONBOARDED');
  }

  const { organizationId, role } = await db.transaction(async (tx) => {
    const [organization]: QueryResponse[] = await tx
      .select({
        role: membershipTable.roleId,
        organizationId: organizationTable.id,
      })
      .from(membershipTable)
      .innerJoin(
        organizationTable,
        eq(membershipTable.organizationId, organizationTable.id)
      )
      .where(eq(membershipTable.userId, userId))
      .limit(1);

    const { role, organizationId } = organization ?? {};

    if (!organizationId || role === undefined) {
      logger.error('❌ ERROR GETTING USER ORGANIZATION DATA.');
      return permanentRedirect(SiteConfig.paths.onboarding);
    }

    return { organizationId, role };
  });

  const canUserChangeBilling = canChangeBilling(role);

  if (!canUserChangeBilling) {
    logger.error(
      { userId, organizationId },
      '❌ USER DOES NOT HAVE PERMISSION TO CHANGE BILLING'
    );
    throw new Error('❌ USER DOES NOT HAVE PERMISSION TO CHANGE BILLING');
  }

  return organizationId;
}

export function getPriceIdByPlan(plan: SubscriptionPlanType) {
  const basicPriceId = process.env.STRIPE_BASIC_PRICE_ID;
  const premiumPriceId = process.env.STRIPE_PREMIUM_PRICE_ID;

  if (plan === 'BASIC') {
    return basicPriceId;
  }

  return premiumPriceId;
}

export function getSeatsByPlan(plan: SubscriptionPlanType) {
  return (
    StripeConfig.plans.find((p) => p.name.toUpperCase() === plan.toUpperCase())
      ?.minSeats ?? 3
  );
}

export function getCancelAtDate(createdAt: number) {
  return Math.floor(createdAt + 365 * 24 * 60 * 60); // 1 year from createdAt
}
