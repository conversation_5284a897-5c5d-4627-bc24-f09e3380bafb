import 'server-only';

import type { AppData, Maybe } from '@/types';
import type { Supabase } from '@packages/supabase/types';
import { unstable_cache as cache } from 'next/cache';
import {
  getURLFromRedirectError,
  redirect,
} from 'next/dist/client/components/redirect';
import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { SiteConfig } from '@/configuration';
import {
  CacheKey,
  Caching,
  defaultRevalidateTimeInSeconds,
} from '@/lib/caching';
import { getAuthUser } from '@packages/supabase/auth-fns/get-auth-user';
import { logger } from '@packages/utils/logger';
import { getUserSessionAndOrganization } from './utils';

export async function loadOnboardingData(
  supabase: Supabase
): Promise<Maybe<AppData>> {
  try {
    const { authUser, error } = await getAuthUser(supabase);

    if (error || !authUser) {
      logger.error({ error }, 'Error in getting Auth User.');
      return redirect(SiteConfig.paths.auth.signIn);
    }

    return cache(
      async () => {
        return await getUserSessionAndOrganization({
          authUser,
          isMembershipRequired: false,
          redirectPath: SiteConfig.paths.app.dashboard,
        });
      },
      Caching.createKeyParts(CacheKey.OnboardingData, authUser.id),
      {
        revalidate: defaultRevalidateTimeInSeconds,
        tags: [Caching.createTag(CacheKey.OnboardingData, authUser.id)],
      }
    )();
  } catch (error) {
    // if the error is a redirect error, we simply redirect the user
    // to the destination URL extracted from the error
    if (isRedirectError(error)) {
      return redirect(getURLFromRedirectError(error));
    }

    logger.warn({ error }, 'Error in loading application data.');

    // in case of any error, we redirect the user to the home page
    // to avoid any potential infinite loop
    return redirect(SiteConfig.paths.app.dashboard);
  }
}
