import type { AppData, Maybe } from '@/types';
import type { MembershipResponse } from '@packages/database/types';
import type { User as AuthUser } from '@packages/supabase/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  addressTable,
  membershipTable,
  organizationAddressTable,
  organizationSubscriptionTable,
  organizationTable,
  subscriptionTable,
  usersTable,
} from '@packages/database/tables';
import { logger } from '@packages/utils/logger';
import { redirect } from 'next/navigation';

export async function getUserSessionAndOrganization(props: {
  authUser: AuthUser;
  redirectPath?: string;
  isUserRequired?: boolean;
  isMembershipRequired?: boolean;
}): Promise<Maybe<AppData>> {
  const {
    authUser,
    redirectPath,
    isUserRequired = true,
    isMembershipRequired = true,
  } = props;

  try {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const membership: Partial<MembershipResponse> = await db.transaction(
      async (tx) => {
        const [result] = await tx
          .select({
            user: {
              id: usersTable.id,
              avatar: usersTable.avatar,
              username: usersTable.username,
              firstName: usersTable.firstName,
              lastName: usersTable.lastName,
              headline: usersTable.headline,
              biography: usersTable.biography,
              fteAvailable: usersTable.fteAvailable,
              obEmailSent: usersTable.obEmailSent,
              payRateId: usersTable.payRateId,
              hasCompletedTour: usersTable.hasCompletedTour,
            },
            organization: {
              id: organizationTable.id,
              ein: organizationTable.ein,
              name: organizationTable.name,
              logo: organizationTable.logo,
              description: organizationTable.description,
              cageCode: organizationTable.cageCode,
              primaryNaicsCode: organizationTable.primaryNaicsCode,
            },
            address: {
              id: addressTable.id,
              address1: addressTable.address1,
              address2: addressTable.address2,
              city: addressTable.city,
              state: addressTable.state,
              zipCode: addressTable.zipCode,
            },
            roleId: membershipTable.roleId,
            customerId: organizationSubscriptionTable.customerId,
            subscription: subscriptionTable,
          })
          .from(usersTable)
          .leftJoin(membershipTable, eq(usersTable.id, membershipTable.userId))
          .leftJoin(
            organizationTable,
            eq(membershipTable.organizationId, organizationTable.id)
          )
          .leftJoin(
            organizationAddressTable,
            eq(organizationTable.id, organizationAddressTable.organizationId)
          )
          .leftJoin(
            addressTable,
            eq(organizationAddressTable.addressId, addressTable.id)
          )
          .leftJoin(
            organizationSubscriptionTable,
            eq(
              organizationSubscriptionTable.organizationId,
              organizationTable.id
            )
          )
          .leftJoin(
            subscriptionTable,
            eq(
              organizationSubscriptionTable.subscriptionId,
              subscriptionTable.id
            )
          )
          .where(eq(usersTable.id, userId))
          .limit(1);

        const {
          user,
          roleId,
          organization,
          address,
          customerId,
          subscription,
        } = result ?? {};

        if (isUserRequired && !user) {
          logger.error('❌ FAILED TO RETRIEVE USER');
          throw new Error('❌ FAILED TO RETRIEVE USER');
        }

        if (
          isMembershipRequired &&
          (roleId === undefined || roleId === null || !organization)
        ) {
          logger.error('❌ FAILED TO RETRIEVE MEMBERSHIP');
          throw new Error('❌ FAILED TO RETRIEVE MEMBERSHIP');
        }

        return {
          user,
          roleId: roleId ?? undefined,
          organization: organization
            ? {
                ...organization,
                address: address ?? undefined,
              }
            : undefined,
          subscription: subscription
            ? {
                customerId: customerId ?? undefined,
                ...subscription,
              }
            : undefined,
        };
      }
    );

    return createAppData(authUser, membership);
  } catch (error) {
    logger.error({ error }, '❌ ERROR IN LOADING APP DATA');
    if (!!redirectPath) {
      return redirect(redirectPath);
    }
  }
}
function createAppData(
  { email, app_metadata }: AuthUser,
  { user, roleId, organization }: Partial<MembershipResponse>
): AppData {
  const { isClient, isOnboarded } = app_metadata ?? {};

  return {
    organization,
    userSession: {
      user: user && {
        ...user,
        role: roleId,
        email,
        isClient,
        isOnboarded,
      },
    },
  };
}

export function getEmptyAppData() {
  return {
    organization: undefined,
    userSession: undefined,
  };
}
