import type { Supabase } from '@packages/supabase/types';
import type { AuthUser } from '@packages/supabase/types';
import type { NextRequest } from 'next/server';

import { SiteConfig } from '@/configuration';
import { getAuthUser } from '@packages/supabase/auth-fns/get-auth-user';
import { getSupabaseServerClient } from '@packages/supabase/server';
import { getRateLimiter } from '@packages/upstash';
import { getClientIp } from '@packages/utils/http';
import { logger } from '@packages/utils/logger';

type RouteParams = Promise<Record<string, string | string[] | undefined>>;

type AuthHandler = (
  req: NextRequest,
  context: {
    params: RouteParams;
    supabase: Supabase;
    authUser: AuthUser;
  }
) => Promise<Response>;

type RateLimitedHandler = (
  req: NextRequest,
  context: { params: RouteParams }
) => Promise<Response>;

export function withRateLimit(
  rateLimit?: number
): (handler: RateLimitedHandler) => RateLimitedHandler {
  return (handler) => async (req, ctx) => {
    if (!SiteConfig.isLocalEnvironment) {
      const ip = await getClientIp();
      const path = req.nextUrl.pathname;
      const identifier = `${ip}-${path}`;
      const rateLimiter = getRateLimiter(rateLimit);

      const { success, remaining } = await rateLimiter.limit(identifier);

      if (!success) {
        logger.error({ remaining }, '❌ RATE LIMIT EXCEEDED');
        return new Response(
          JSON.stringify({
            error: 'Too many requests',
          }),
          {
            status: 429,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }
    }

    return handler(req, ctx);
  };
}

export function withAuth(
  handler: AuthHandler,
  rateLimit?: number
): RateLimitedHandler {
  const rateLimitedHandler = async (
    req: NextRequest,
    ctx: { params: RouteParams }
  ) => {
    const supabase = await getSupabaseServerClient();
    const { authUser, error } = await getAuthUser(supabase);

    if (error || !authUser) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const enrichedCtx = {
      ...ctx,
      supabase,
      authUser,
    };

    return handler(req, enrichedCtx);
  };

  return withRateLimit(rateLimit)(rateLimitedHandler);
}
